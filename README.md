# <PERSON> - Personal Blog & Portfolio

A modern, minimalist personal blog and portfolio website built with Next.js 14, TypeScript, and Tailwind CSS.
<!-- Force redeploy: 2025-06-12 -->

## Features

- 🎨 **Modern Design**: Clean, minimalist interface with subtle animations
- 📝 **Markdown Blog**: Easy content management with markdown files
- 🎯 **Project Showcase**: Portfolio grid with detailed project pages
- 📱 **Responsive**: Optimized for all devices
- 🚀 **Performance**: Fast loading with Next.js optimizations
- 📧 **Smart Forms**: Contact forms with location-based features
- 🔍 **SEO Optimized**: Meta tags and structured data
- ♿ **Accessible**: WCAG compliant design

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Content**: Markdown with gray-matter
- **Email**: EmailJS integration ready

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/ernestromelo/blog.git
cd blog
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Content Management

### Adding Blog Posts

1. Create a new `.md` file in `content/blog/`
2. Use the template in `content/blog/template.md`
3. Add your featured image to `public/images/blog/`
4. The post will automatically appear on the blog

### Adding Projects

1. Create a new `.md` file in `content/projects/`
2. Use the template in `content/projects/template.md`
3. Add project images to `public/images/projects/`
4. The project will automatically appear in the portfolio

### Image Guidelines

- **Avatar**: 200x200px, square, place in `public/images/avatar.jpg`
- **Blog Featured Images**: 1200x630px, place in `public/images/blog/`
- **Project Images**: 1200x630px for featured, any size for gallery

## Customization

### Personal Information

Update the following files with your information:
- `src/app/layout.tsx` - Site metadata
- `src/components/Header.tsx` - Your name and avatar
- `content/blog/welcome-to-my-blog.md` - Introduction post

### Email Configuration

To enable contact forms:
1. Sign up for EmailJS
2. Update `src/lib/email.ts` with your service configuration
3. Replace console.log statements with actual EmailJS calls

### Styling

- Colors and themes: `tailwind.config.ts`
- Global styles: `src/app/globals.css`
- Component styles: Individual component files

## Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy automatically

### Other Platforms

The site is a static Next.js app and can be deployed to:
- Netlify
- GitHub Pages
- AWS S3 + CloudFront
- Any static hosting service

## Project Structure

```
├── content/
│   ├── blog/           # Blog posts (markdown)
│   └── projects/       # Project pages (markdown)
├── public/
│   └── images/         # Static images
├── src/
│   ├── app/           # Next.js app router pages
│   ├── components/    # React components
│   ├── lib/          # Utility functions
│   └── types/        # TypeScript types
├── package.json
└── README.md
```

## Contributing

This is a personal website, but feel free to:
- Report bugs
- Suggest improvements
- Use as inspiration for your own site

## License

MIT License - feel free to use this code for your own personal website.

## Contact

- Website: [ernestromelo.com](https://ernestromelo.com)
- Email: <EMAIL>
- Projects: <EMAIL>
