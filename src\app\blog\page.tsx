import { getBlogPosts } from '@/lib/markdown'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'
import SubtleGradientBackground from '@/components/SubtleGradientBackground'

export default async function BlogPage() {
  const posts = await getBlogPosts()

  return (
    <>
      <SubtleGradientBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 animate-fade-in">
            Blog
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-slide-up">
            Explore my thoughts on web development, AI automation, and technology.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="mb-20">
          <BlogGridWithFilter posts={posts} />
        </div>
      </div>
    </>
  )
}
